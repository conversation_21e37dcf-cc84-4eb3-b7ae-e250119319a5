var img = [
  "@/assets/tableicon/line1.png",
  "@/assets/tableicon/line2.png",
  "@/assets/tableicon/line3.png",
];
var color = ["#00f8ff", "#00f15a", "#0696f9", "#dcf776"];

// 计算合适的Y轴刻度间隔
function calculateInterval(min, max) {
  const range = max - min;
  if (range === 0) return max * 0.2; // 如果范围为0，返回最大值的20%

  // 计算数量级
  const magnitude = Math.pow(10, Math.floor(Math.log10(range)));
  const normalizedRange = range / magnitude;

  let interval;
  if (normalizedRange <= 1) {
    interval = magnitude * 0.2;
  } else if (normalizedRange <= 2) {
    interval = magnitude * 0.5;
  } else if (normalizedRange <= 5) {
    interval = magnitude;
  } else {
    interval = magnitude * 2;
  }

  return interval;
}

// 格式化Y轴标签
function formatYAxisLabel(value) {
  if (value >= 10000) {
    return (value / 10000).toFixed(1) + '万';
  } else if (value >= 1000) {
    return (value / 1000).toFixed(1) + 'k';
  } else {
    return value.toFixed(1);
  }
}

// 默认配置
function getDefaultOption() {
  return {
    backgroundColor: "#0e2147",
    grid: {
      left: "32px",
      bottom: "32px",
    },
    legend: {
      type: "scroll",
      data: [],
      itemWidth: 18,
      itemHeight: 12,
      textStyle: {
        color: "#00ffff",
        fontSize: 14,
      },
    },
    yAxis: [
      {
        type: "value",
        position: "right",
        splitLine: { show: false },
        axisLine: { show: false },
        axisTick: { show: false },
        axisLabel: { show: false },
      },
      {
        type: "value",
        position: "left",
        min: 0,
        max: 100,
        interval: 20,
        nameTextStyle: { color: "#00FFFF" },
        splitLine: {
          lineStyle: {
            type: "dashed",
            color: "rgba(135,140,147,0.8)",
          },
        },
        axisLine: { show: false },
        axisTick: { show: false },
        axisLabel: {
          formatter: "{value}",
          color: "#fff",
          fontSize: 14,
        },
      },
    ],
    xAxis: [
      {
        type: "category",
        axisTick: { show: false },
        axisLine: { show: false },
        axisLabel: {
          inside: false,
          textStyle: {
            color: "#fff",
            fontWeight: "normal",
            fontSize: "14",
            lineHeight: 22,
          },
        },
        data: [],
      },
    ],
    series: [],
  };
}

export function getOption(xData, yData, Line) {
  console.log("折线图获取的数据", { xData, yData, Line });

  // 数据验证
  if (!yData || yData.length === 0 || !xData || xData.length === 0) {
    console.warn('折线图数据为空');
    return getDefaultOption();
  }

  // 计算所有数据的最大值和最小值，用于Y轴范围设置
  const allValues = yData.flat().filter(val => val != null && !isNaN(val) && val >= 0);

  if (allValues.length === 0) {
    console.warn('折线图没有有效数据');
    return getDefaultOption();
  }

  const maxValue = Math.max(...allValues);
  const minValue = Math.min(...allValues);

  // 计算合适的Y轴范围
  let yAxisMin, yAxisMax, interval;

  if (allValues.length === 1) {
    // 只有一个数据点的特殊处理
    const singleValue = allValues[0];
    yAxisMin = Math.max(0, singleValue * 0.8);
    yAxisMax = singleValue * 1.2;
    interval = (yAxisMax - yAxisMin) / 5; // 分成5个刻度
  } else {
    // 多个数据点的正常处理
    const range = maxValue - minValue;
    const padding = range > 0 ? range * 0.1 : maxValue * 0.1; // 10%的边距
    yAxisMin = Math.max(0, minValue - padding);
    yAxisMax = maxValue + padding;
    interval = calculateInterval(yAxisMin, yAxisMax);
  }

  var datas = [];
  Line.forEach((item, index) => {
    if (index >= yData.length) return; // 防止数组越界

    // 过滤掉无效数据点
    const currentData = yData[index] || [];
    const validData = currentData.filter(val => val != null && !isNaN(val) && val >= 0);
    const avgValue = validData.length > 0 ?
      validData.reduce((a, b) => a + b, 0) / validData.length : 0;

    datas.push({
      name: item,
      type: "line",
      yAxisIndex: 1,
      data: currentData,
      smooth: true, // 添加平滑曲线
      symbol: 'circle', // 数据点样式
      symbolSize: 8, // 数据点大小
      lineStyle: {
        width: 3, // 线条宽度
      },
      itemStyle: {
        normal: {
          borderWidth: 2,
          color: color[index % color.length], // 防止颜色数组越界
          borderColor: '#fff',
        },
      },
      markLine: validData.length > 1 ? { // 只有多个数据点时才显示平均线
        symbol: "none",
        lineStyle: {
          color: color[index % color.length],
          type: "dashed",
          width: 2,
        },
        data: [
          {
            name: "平均值",
            yAxis: avgValue,
          }
        ]
      } : undefined
    });
  });

  let option = {
    backgroundColor: "#0e2147",
    grid: {
      left: "32px",
      bottom: "32px",
    },
    legend: {
      type: "scroll",
      data: Line,
      itemWidth: 18,
      itemHeight: 12,
      textStyle: {
        color: "#00ffff",
        fontSize: 14,
      },
    },
    yAxis: [
      {
        type: "value",
        position: "right",
        splitLine: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: false,
        },
      },
      {
        type: "value",
        position: "left",
        min: allValues.length > 0 ? yAxisMin : 0,
        max: allValues.length > 0 ? yAxisMax : 100,
        interval: allValues.length > 0 ? interval : 20,
        nameTextStyle: {
          color: "#00FFFF",
        },
        splitLine: {
          lineStyle: {
            type: "dashed",
            color: "rgba(135,140,147,0.8)",
          },
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          formatter: function(value) {
            return formatYAxisLabel(value);
          },
          color: "#fff",
          fontSize: 14,
        },
      },
    ],
    xAxis: [
      {
        type: "category",
        axisTick: {
          show: false,
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: "#6A989E",
          },
        },
        axisLabel: {
          inside: false,
          textStyle: {
            color: "#fff", // x轴颜色
            fontWeight: "normal",
            fontSize: "14",
            lineHeight: 22,
          },
        },
        data: xData,
      },
    ],
    series: datas,
  };
  return option;
}
