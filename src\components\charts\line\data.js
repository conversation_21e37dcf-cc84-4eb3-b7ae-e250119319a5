var img = [
  "@/assets/tableicon/line1.png",
  "@/assets/tableicon/line2.png",
  "@/assets/tableicon/line3.png",
];
var color = ["#00f8ff", "#00f15a", "#0696f9", "#dcf776"];

// 计算合适的Y轴刻度间隔
function calculateInterval(min, max) {
  const range = max - min;
  if (range === 0) return max * 0.2; // 如果范围为0，返回最大值的20%

  // 目标刻度数量（4-6个刻度比较合适）
  const targetTickCount = 5;
  const roughInterval = range / targetTickCount;

  // 计算数量级
  const magnitude = Math.pow(10, Math.floor(Math.log10(roughInterval)));
  const normalizedInterval = roughInterval / magnitude;

  // 选择合适的间隔倍数
  let niceInterval;
  if (normalizedInterval <= 1) {
    niceInterval = magnitude;
  } else if (normalizedInterval <= 2) {
    niceInterval = magnitude * 2;
  } else if (normalizedInterval <= 5) {
    niceInterval = magnitude * 5;
  } else {
    niceInterval = magnitude * 10;
  }

  return niceInterval;
}

// 格式化Y轴标签 - 返回纯数字格式，不添加单位
function formatYAxisLabel(value) {
  // 根据数值大小选择合适的精度
  if (Math.abs(value) >= 10000) {
    // 大于等于10000的数值，保留1位小数
    return value.toFixed(1);
  } else if (Math.abs(value) >= 100) {
    // 100-9999的数值，保留1位小数
    return value.toFixed(1);
  } else if (Math.abs(value) >= 1) {
    // 1-99的数值，保留2位小数
    return value.toFixed(2);
  } else {
    // 小于1的数值，保留3位小数
    return value.toFixed(3);
  }
}

// 默认配置
function getDefaultOption() {
  return {
    backgroundColor: "#0e2147",
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
    legend: {
      type: "scroll",
      data: [],
      itemWidth: 18,
      itemHeight: 12,
      textStyle: {
        color: "#00ffff",
        fontSize: 14,
      },
    },
    yAxis: [
      {
        type: "value",
        position: "right",
        splitLine: { show: false },
        axisLine: { show: false },
        axisTick: { show: false },
        axisLabel: { show: false },
      },
      {
        type: "value",
        position: "left",
        min: 0,
        max: 100,
        interval: 20,
        nameTextStyle: { color: "#00FFFF" },
        splitLine: {
          lineStyle: {
            type: "dashed",
            color: "rgba(135,140,147,0.8)",
          },
        },
        axisLine: { show: false },
        axisTick: { show: false },
        axisLabel: {
          formatter: "{value}",
          color: "#fff",
          fontSize: 14,
        },
      },
    ],
    xAxis: [
      {
        type: "category",
        axisTick: { show: false },
        axisLine: { show: false },
        axisLabel: {
          inside: false,
          textStyle: {
            color: "#fff",
            fontWeight: "normal",
            fontSize: "14",
            lineHeight: 22,
          },
        },
        data: [],
      },
    ],
    series: [],
  };
}

export function getOption(xData, yData, Line) {
  console.log("折线图获取的数据", { xData, yData, Line });

  // 数据验证
  if (!yData || yData.length === 0 || !xData || xData.length === 0) {
    console.warn("折线图数据为空");
    return getDefaultOption();
  }

  // 计算所有数据的最大值和最小值，用于Y轴范围设置
  const allValues = yData
    .flat()
    .filter((val) => val != null && !isNaN(val) && val >= 0);

  if (allValues.length === 0) {
    console.warn("折线图没有有效数据");
    return getDefaultOption();
  }

  const maxValue = Math.max(...allValues);
  const minValue = Math.min(...allValues);

  // 计算合适的Y轴范围
  let yAxisMin, yAxisMax, interval;

  if (allValues.length === 1) {
    // 只有一个数据点的特殊处理
    const singleValue = allValues[0];
    yAxisMin = Math.max(0, singleValue * 0.8);
    yAxisMax = singleValue * 1.2;
    interval = (yAxisMax - yAxisMin) / 5; // 分成5个刻度
  } else {
    // 多个数据点的正常处理
    const range = maxValue - minValue;
    const padding = range > 0 ? range * 0.1 : maxValue * 0.1; // 10%的边距
    yAxisMin = Math.max(0, minValue - padding);
    yAxisMax = maxValue + padding;
    interval = calculateInterval(yAxisMin, yAxisMax);
  }

  var datas = [];
  Line.forEach((item, index) => {
    if (index >= yData.length) return; // 防止数组越界

    // 过滤掉无效数据点
    const currentData = yData[index] || [];
    const validData = currentData.filter(
      (val) => val != null && !isNaN(val) && val >= 0
    );
    const avgValue =
      validData.length > 0
        ? validData.reduce((a, b) => a + b, 0) / validData.length
        : 0;

    // 构建基础系列配置
    const seriesConfig = {
      name: item,
      type: "line",
      yAxisIndex: 1,
      data: currentData,
      smooth: true, // 添加平滑曲线
      symbol: "circle", // 数据点样式
      symbolSize: 8, // 数据点大小
      lineStyle: {
        width: 3, // 线条宽度
        color: color[index % color.length], // 防止颜色数组越界
      },
      itemStyle: {
        normal: {
          borderWidth: 2,
          color: color[index % color.length],
          borderColor: "#fff",
        },
      },
    };

    // 平均值线条配置 - 改进显示逻辑
    if (validData.length > 0) {
      // 只要有有效数据就显示平均值线，包括单数据点情况
      seriesConfig.markLine = {
        silent: true, // 不响应鼠标事件
        symbol: "none", // 不显示端点符号
        lineStyle: {
          color: color[index % color.length],
          type: "dashed",
          width: 2,
          opacity: 0.8, // 设置透明度，使平均线不会过于突出
        },
        label: {
          show: true,
          position: "end",
          formatter: function (params) {
            return `平均值: ${formatYAxisLabel(params.value)}`;
          },
          color: color[index % color.length],
          fontSize: 12,
          backgroundColor: "rgba(0,0,0,0.6)",
          padding: [4, 8],
          borderRadius: 4,
        },
        data: [
          {
            name: "平均值",
            yAxis: avgValue,
          },
        ],
      };
    }

    datas.push(seriesConfig);
  });

  let option = {
    backgroundColor: "#0e2147",
    grid: {
      left: "80px", // 增加左边距，为Y轴标签留出更多空间
      right: "20px",
      top: "20px",
      bottom: "40px",
      containLabel: true, // 确保标签完全显示在网格内
    },
    legend: {
      type: "scroll",
      data: Line,
      itemWidth: 18,
      itemHeight: 12,
      textStyle: {
        color: "#00ffff",
        fontSize: 14,
      },
    },
    yAxis: [
      {
        type: "value",
        position: "right",
        splitLine: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: false,
        },
      },
      {
        type: "value",
        position: "left",
        min: allValues.length > 0 ? yAxisMin : 0,
        max: allValues.length > 0 ? yAxisMax : 100,
        interval: allValues.length > 0 ? interval : 20,
        nameTextStyle: {
          color: "#00FFFF",
        },
        splitLine: {
          lineStyle: {
            type: "dashed",
            color: "rgba(135,140,147,0.8)",
          },
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          formatter: function (value) {
            return formatYAxisLabel(value);
          },
          color: "#fff",
          fontSize: 14,
          margin: 15, // 增加标签与轴线的距离
          showMaxLabel: true, // 确保显示最大值标签
          showMinLabel: true, // 确保显示最小值标签
          inside: false, // 标签显示在轴线外侧
          rotate: 0, // 标签不旋转，保持水平
        },
      },
    ],
    xAxis: [
      {
        type: "category",
        axisTick: {
          show: false,
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: "#6A989E",
          },
        },
        axisLabel: {
          inside: false,
          textStyle: {
            color: "#fff", // x轴颜色
            fontWeight: "normal",
            fontSize: "14",
            lineHeight: 22,
          },
        },
        data: xData,
      },
    ],
    series: datas,
  };
  return option;
}
