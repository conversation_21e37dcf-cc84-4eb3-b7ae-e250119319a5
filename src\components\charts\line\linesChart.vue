<template>
  <div class="chart-container">
    <div class="chart" ref="chart"></div>
  </div>
</template>
<script>
import * as echarts from "echarts";
import { getOption } from "./data";
export default {
  name: "linesChart",
  props: {
    legendData: {
      type: Array,
      default: () => [],
    },
    xData: {
      type: Array,
      default: () => [],
    },
    yData: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    yData: {
      handler() {
        this.$nextTick(() => {
          this.initChart();
        });
      },
      deep: true,
    },
    xData: {
      handler() {
        this.$nextTick(() => {
          this.initChart();
        });
      },
      deep: true,
    },
    legendData: {
      handler() {
        this.$nextTick(() => {
          this.initChart();
        });
      },
      deep: true,
    },
  },
  data() {
    return {
      chartInstance: null,
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
    // 监听窗口大小变化
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    if (this.chartInstance) {
      this.chartInstance.dispose();
      this.chartInstance = null;
    }
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    initChart() {
      if (!this.$refs.chart) {
        return;
      }

      // 销毁之前的图表实例
      if (this.chartInstance) {
        this.chartInstance.dispose();
      }

      // 检查数据有效性
      if (!this.yData || this.yData.length === 0 || !this.xData || this.xData.length === 0) {
        console.warn('折线图数据为空或无效');
        return;
      }

      this.chartInstance = echarts.init(this.$refs.chart);
      const option = getOption(this.xData, this.yData, this.legendData);
      this.chartInstance.setOption(option, true);

      console.log('折线图初始化完成', {
        xData: this.xData,
        yData: this.yData,
        legendData: this.legendData
      });
    },
    handleResize() {
      if (this.chartInstance) {
        this.chartInstance.resize();
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.chart-container {
  width: 100%;
  height: 100%;
  flex: 1;
  .chart {
    z-index: 1;
  }
  .chart {
    width: 100%;
    height: 100%;
  }
}
</style>
