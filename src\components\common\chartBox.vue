<template>
  <div id="chart-box">
    <div class="title-box">
      <div id="title">
        <div id="title-left">
          <img src="@/assets/tableicon/left-icon.png" alt="" />
          <span> {{ title }}</span>
        </div>
      </div>
      <slot name="box-right" class="box-right"></slot>
    </div>
    <slot></slot>
  </div>
</template>
<script>
export default {
  props: {
    title: {
      type: String,
      default: "",
    },
  },
  data() {
    return {};
  },
  mounted() {},
  computed: {},
  methods: {},
};
</script>
<style lang='scss' scoped>
.title-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
}
#title {
  flex: 1;
  margin-right: 5%;
  font-family: "SourceHanSansCN-Medium", Arial;
}
[data-theme="tint"] #chart-box {
  box-sizing: border-box;
  max-width: 100%;
  height: 100%;
  background: #fff;
  color: #000;
  border-radius: 10px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}
[data-theme="tint"] .title-box {
  // width: 100%;
  height: 40px;
  color: #000;
  font-size: 16px;
  border-radius: 8px 8px 0px 0px;
  background: #ecf7ff;
  font-family: "SourceHanSansCN-Bold", Arial;
  display: flex;
  align-items: center

  img {
    width: 20px;
    height: 20px;
    margin-right: 10px;
  }
}

[data-theme="tint"] #title-left {
  max-width: 412px;
  height: 40px;
  background-image: url("@/assets/tableicon/left-tintbg.png");
  background-size: 100% 100%;
  display: flex;
  align-items: center;
  padding: 0 19px;
}

[data-theme="dark"] #chart-box {
  max-width: 100%;
  height: 100%;
  box-sizing: border-box;
  background: #1b2242;
  border-radius: 10px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}
[data-theme="dark"] .title-box {
  // width: 100%;
  height: 40px;
  display: flex;
  align-items: center;
  color: #fff;
  font-size: 16px;
  border-radius: 8px 8px 0px 0px;
  background: #1a2e5e;
  img {
    width: 20px;
    height: 20px;
    margin-right: 10px;
  }
}

[data-theme="dark"] #title-left {
  max-width: 412px;
  height: 40px;
  background-image: url("@/assets/tableicon/left-darkbg.png");
  background-size: 100% 100%;
  display: flex;
  align-items: center;
  padding: 0 19px;
}
</style>
