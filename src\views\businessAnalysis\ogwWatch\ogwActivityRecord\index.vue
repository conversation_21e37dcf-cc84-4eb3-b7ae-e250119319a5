<template>
  <div class="ogwActivityRecord">
    <div class="head-box">
      <ul>
        <li
          @click="toInfo('use')"
          :class="{ active: isActive === 'useChemical' }"
        >
          化学药剂使用
        </li>
        <li
          @click="toInfo('filter')"
          :class="{ active: isActive === 'filterChemical' }"
        >
          化学药剂筛选
        </li>
        <li
          @click="toInfo('evaluation')"
          :class="{ active: isActive === 'evaluateChemical' }"
        >
          化学药剂评价
        </li>
        <li
          @click="toInfo('test')"
          :class="{ active: isActive === 'testChemical' }"
        >
          分析化验
        </li>
      </ul>
    </div>
    <div class="content-box">
      <router-view></router-view>
    </div>
  </div>
</template>
<script>
export default {
  name: "ogwActivityRecord",
  mounted() {
    // 初始化路由状态
    this.isActive = this.$route.name;

    // 初始化主题监听
    this.initThemeObserver();

    // 恢复当前主题状态
    this.restoreTheme();
  },
  beforeDestroy() {
    // 清理主题监听器
    if (this.themeObserver) {
      this.themeObserver.disconnect();
    }
  },
  data() {
    return {
      isActive: "use",
      currentTheme: "dark", // 当前主题
      themeObserver: null, // 主题变化监听器
    };
  },
  computed: {
    // 监听store中的主题状态
    curTheme() {
      return this.$store.state.curTheme;
    },
  },
  watch: {
    // 监听store主题变化
    curTheme: {
      handler(newValue) {
        this.currentTheme = newValue ? 'dark' : 'tint';
      },
      immediate: true
    }
  },
  methods: {
    toInfo(name) {
      switch (name) {
        case "use":
          this.$router.push({ name: "useChemical" });
          this.isActive = "useChemical";
          break;
        case "filter":
          this.$router.push({ name: "filterChemical" });
          this.isActive = "filterChemical";
          break;
        case "evaluation":
          this.$router.push({ name: "evaluateChemical" });
          this.isActive = "evaluateChemical";
          break;
        case "test":
          this.$router.push({ name: "testChemical" });
          this.isActive = "testChemical";
          break;
      }
    },



    // 恢复当前主题状态
    restoreTheme() {
      // 从document属性读取当前主题
      const currentDocTheme = document.documentElement.getAttribute('data-theme');
      if (currentDocTheme && (currentDocTheme === 'dark' || currentDocTheme === 'tint')) {
        this.currentTheme = currentDocTheme;
      } else {
        // 如果没有设置主题，使用默认深色主题
        this.currentTheme = 'dark';
      }
    },

    // 初始化主题变化监听器
    initThemeObserver() {
      // 监听document的data-theme属性变化
      this.themeObserver = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'attributes' && mutation.attributeName === 'data-theme') {
            const newTheme = document.documentElement.getAttribute('data-theme');
            if (newTheme && newTheme !== this.currentTheme) {
              this.currentTheme = newTheme;
              this.$store.commit('changeTheme', newTheme === 'dark' ? 1 : 0);
            }
          }
        });
      });

      this.themeObserver.observe(document.documentElement, {
        attributes: true,
        attributeFilter: ['data-theme']
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.ogwActivityRecord {
  width: 100%;
  height: 100%;
  padding: 20px;

  .head-box {
    width: 100%;
    height: 50px;
    border-radius: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    ul {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      border-bottom: 1px solid #f0f0f0;
      transition: border-color 0.3s ease;

      li {
        cursor: pointer;
        margin-right: 40px;
        position: relative;
        padding: 0 8px;
        height: 100%;
        display: flex;
        align-items: center;
        transition: all 0.3s ease;
        border-radius: 4px;

        &:hover {
          background: rgba(22, 119, 255, 0.05);
          color: #1677ff;
        }
      }
    }
  }

  .content-box {
    margin-top: 20px;
  }
}

// 默认激活状态样式
.active {
  color: #1677ff !important;
  font-weight: 500;
}

.active::after {
  content: "";
  display: block;
  width: 100%;
  height: 2px;
  background: #1677ff;
  position: absolute;
  bottom: -1px;
  left: 0;
  transition: all 0.3s ease;
}

/* 深色主题样式 */
[data-theme="dark"] .ogwActivityRecord {
  background: #162549;

  .head-box {
    background: #1A2E52;
    color: #CCE4FF;
    border: 1px solid #4F98F6;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);

    ul {
      border-bottom: 1px solid #4F98F6;

      li {
        color: #CCE4FF;

        &:hover {
          background: rgba(79, 152, 246, 0.1);
          color: #4EA0FC;
        }
      }
    }
  }

  .active {
    color: #4EA0FC !important;
  }

  .active::after {
    background: #4EA0FC;
  }
}

/* 浅色主题样式 */
[data-theme="tint"] .ogwActivityRecord {
  background: #E7EFF9;

  .head-box {
    background: #fff;
    color: #2E3641;
    border: 1px solid #EAEFF5;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

    ul {
      border-bottom: 1px solid #EAEFF5;

      li {
        color: #2E3641;

        &:hover {
          background: rgba(22, 119, 255, 0.05);
          color: #1677ff;
        }
      }
    }
  }

  .active {
    color: #1677ff !important;
  }

  .active::after {
    background: #1677ff;
  }
}
</style>
