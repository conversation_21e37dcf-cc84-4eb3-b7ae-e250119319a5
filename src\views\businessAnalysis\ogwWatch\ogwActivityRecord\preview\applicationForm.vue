<template>
  <div class="applicationForm">
    <div class="preview-box">
      <pdf-viewer v-if="fileUrl" :pdf-url="fileUrl"></pdf-viewer>
    </div>
    <div class="content">
      <el-card class="upload-card">
        <div slot="header" class="btns">
          <el-upload
            class="upload-demo"
            :action='`${prefix}/attachment/uploadFile`'
            :headers="uploadHeaders"
            :on-success="handleSuccess"
            :limit="3"
            :on-exceed="handleExceed"
            :file-list="fileList"
            :show-file-list="false"
          >
            <el-button type="primary" class="confirm-btn">上传</el-button>
          </el-upload>
          <el-button type="primary" class="confirm-btn" @click="handleCancle"
            >返回</el-button
          >
          <el-button type="primary" class="confirm-btn" @click="handleSubmit"
            >确定</el-button
          >
        </div>

        <div class="device-section">
          <el-form ref="form" :model="form" label-width="180px">
            <el-form-item label="使用装置">
              <el-input v-model="form.deviceNameType"></el-input>
            </el-form-item>
            <el-form-item label="原药剂型号">
              <el-input v-model="form.primaryDrugModel"></el-input>
            </el-form-item>

            <el-form-item label="药剂单价">
              <el-input v-model="form.pharmaceuticalPrice">
                <template slot="append">元/公斤</template>
              </el-input>
            </el-form-item>

            <el-form-item label="药剂年度消耗量（公斤）">
              <el-input v-model="form.pharmaceuticalYearConsume"></el-input>
            </el-form-item>

            <el-form-item label="药剂注入浓度">
              <el-input v-model="form.pharmaceuticalInjectConcentration">
                <template slot="append">%</template>
              </el-input>
            </el-form-item>

            <el-form-item label="药剂筛选原因">
              <el-input
                v-model="form.filterReason"
                type="textarea"
                :rows="3"
                placeholder="请输入筛选原因"
              ></el-input>
            </el-form-item>

            <el-form-item label="本次筛选说明">
              <el-input
                v-model="form.thisFilterDescribe"
                type="textarea"
                :rows="3"
                placeholder="请输入本次筛选说明"
              ></el-input>
            </el-form-item>
          </el-form>
        </div>
      </el-card>
    </div>
  </div>
</template>
<script>
import PdfViewer from "@/components/common/pdfView.vue";
import { saveFilterChemicals } from "@/api/ogwActiveRecord/chemicalsServe.js";
import { getFileList, previewFileById } from "@/api/file/index.js";
export default {
  name: "applicationForm",
  components: {
    PdfViewer,
  },
  activated() {
    this.id = this.$route.params.id || null;
    const fileId = this.$route.params.fileIds;
    this.formType = this.$route.params.type;
    this.fileUrl = null;
    this.form = {};
    if (fileId) {
      this.getFile(fileId);
    }
    this.initUploadHeaders();
  },
  mounted() {
    window.document.documentElement.setAttribute("data-theme", "tint");
    this.id = this.$route.params.id || null;
    const fileId = this.$route.params.fileIds;
    this.formType = this.$route.params.type;
    if (fileId) {
      this.getFile(fileId);
    }
    this.prefix = process.env.VUE_APP_PREFIX;
    this.initUploadHeaders();
  },
  data() {
    return {
      id: "",
      form: {
        deviceNameType: "",
        primaryDrugModel: "",
        pharmaceuticalPrice: "",
        pharmaceuticalYearConsume: "",
        pharmaceuticalInjectConcentration: "",
        filterReason: "",
        thisFilterDescribe: "",
        prefix: '',
      },
      fileList: [],
      fileUrl: null,
      formType: null,
      uploadHeaders: {},
    };
  },
  methods: {
    /**
     * 初始化上传请求头，添加身份验证token
     */
    initUploadHeaders() {
      const access_token = localStorage.getItem("access_token");
      this.uploadHeaders = {
        client_id: process.env.VUE_APP_ID,
        client_secret: process.env.VUE_APP_ID,
      };
      if (access_token) {
        this.uploadHeaders.Authorization = access_token;
      }
    },
    handleSubmit() {
      if (this.id) {
        this.form.id = this.id;
      }
      this.submitForm();
    },
    handleSuccess(response, file, fileList) {
      this.$message.success("上传成功");
      this.form.applyFileId = response.data.fileId;
      this.handlePreview(response.data.fileId);
    },
    handleCancle() {
      this.$router.back();
    },
    handleExceed() {},
    /**
     * 新增
     */
    async submitForm() {
      const data = {
        ...this.form,
      };
      let res = null;
      switch (this.formType) {
        case "filter":
          data.type = 1;
          res = await saveFilterChemicals(data);
          break;
        case "evaluate":
          data.type = 2;
          res = await saveFilterChemicals(data);
          break;
      }
      if (res.code === 200) {
        this.$message.success("提交成功");
      }
    },

    async getFile(id) {
      const res = await getFileList(id);
      if (res.code === 200) {
        this.handlePreview(res.data[0].fileId);
      }
    },

    async handlePreview(id) {
      const res = await previewFileById(id);
      if (res.code === 200) {
        this.fileUrl = res.data;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.applicationForm {
  display: flex;
  .preview-box {
    flex: 1;
    height: 100%;
    border-right: 1px solid #ccc;
  }
  .content {
    flex: 1;
    .upload-card {
      max-width: 800px;
      height: 100%;
      margin: 0 auto;
      .btns {
        display: flex;
        justify-content: flex-end;
      }
      .confirm-btn {
        margin-left: 12px;
      }

      .device-section {
        padding: 0 20px;
      }

      .el-form-item {
        margin-bottom: 22px;
      }
    }
  }
}
</style>
