<template>
  <div class="budget-container">
    <TitleBox
      :title="'油气水处理总体费用概况'"
      :amount="overallCost"
      :unit="'万元'"
    ></TitleBox>
    <PieChart
      class="scale"
      :chartData="chartData"
      :position="{ right: 'right', top: 'center' }"
    ></PieChart>
    <TitleBox
      :title="'预算执行'"
      :amount="overallCost"
      :unit="'万元'"
      :greenColor="true"
      @click="checkInfo"
    ></TitleBox>
    <div class="budget-proportion">
      <div class="pro-top">
        <p>预算执行占比</p>
        <p class="pro-number">{{ proportion }}%</p>
      </div>
      <div>
        <el-progress
          :percentage="proportion"
          :stroke-width="10"
          :show-text="false"
          :color="customColors"
        ></el-progress>
      </div>
    </div>
    <DialogBox :title="'费用科目明细'" :isShow.sync="showInfo" :showBtn="true">
      <BorderTable
        :colums="colums"
        :tableData="tableData"
        :showBtn="true"
        :showSummary="true"
      ></BorderTable>
    </DialogBox>
  </div>
</template>
<script>
import PieChart from "@/components/charts/pie/pieChart.vue";
import TitleBox from "../titleBox/index.vue";
import BorderTable from "@/components/comTable/borderTable.vue";
import { getBudgetExecution } from "@/api/ogw/index.js";
export default {
  name: "budgetExecution",
  components: { TitleBox, PieChart, BorderTable },
  props: {
    date: {
      type: String,
    },
  },
  watch: {
    date: {
      handler(val) {
        this.getData();
      },
    },
  },
  computed: {
    overallCost() {
      // 获取infoData中budget的总和，将元换算成万元
      const total = this.infoData.reduce(
        (total, item) => total + item.budget,
        0
      );
      return Number((total / 10000).toFixed(2));
    },
    proportion() {
      let total = this.infoData.reduce(
        (total, item) => total + item.execute,
        0
      );

      total = Number((total / 10000).toFixed(2));
      

      if (this.overallCost === 0) {
        return 0;
      }

      const res = Number(((total / this.overallCost) * 100).toFixed(2));

      if (isNaN(res)) {
        return 0;
      }
      return res;
    },
    tableData() {
      return this.infoData.map((item) => {
        let rate = ((item.execute / item.budget) * 100).toFixed(2) + "%";
        if (isNaN(rate)) {
          rate = "0.00%";
        }
        let execute = (item.execute / 100000).toFixed(2);
        let budget = (item.budget / 100000).toFixed(2);
        return {
          name: item.name,
          budget: budget,
          execute: execute,
          deviate: (budget - execute).toFixed(2),
          rate: rate,
        };
      });
    },
    chartData() {
      // 图表数据
      const color = ["#4F42B9", "#0DBD1F", "#60FFEE"];
      return this.infoData.map((item, index) => {
        return {
          name: item.name,
          value: item.execute,
          itemStyle: { opacity: 1, color: color[index] },
        };
      });
    },
  },
  data() {
    return {
      infoData: [],
      customColors: [
        { color: "#60D2CA", percentage: 0 },
        { color: "#5FD1CB", percentage: 20 },
        { color: "#45CDCF", percentage: 40 },
        { color: "#30CADA", percentage: 60 },
        { color: "#22C8E0", percentage: 80 },
        { color: "#17C6DB", percentage: 100 },
      ],
      colums: [
        { prop: "name", label: "服务项目名称" },
        {
          label: "2025年陵水-崖城作业公司1-5月油气水处理费用/万元",
          children: [
            { label: "预算(万元)", prop: "budget" },
            { label: "执行(万元)", prop: "execute" },
            { label: "偏差(万元)", prop: "deviate" },
            { label: "执行率", prop: "rate" },
          ],
        },
      ],
      showInfo: false,
    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    checkInfo() {
      // console.log("checkInfo");
      this.showInfo = !this.showInfo;
    },
    async getData() {
      const res = await getBudgetExecution(this.date);
      if (res.code === 200) {
        this.infoData = res.data;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.budget-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .scale {
    flex: 3;
  }

  .budget-proportion {
    flex: 1;
    margin: 16px;
    .pro-top {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;

      font-family: Source Han Sans;
      font-size: 14px;
    }
  }
}

[data-theme="dark"] .pro-top {
  color: #fff;
}
[data-theme="dark"] .pro-number {
  color: #1dfbfd;
}
[data-theme="tint"] .pro-top {
  color: rgba(0, 0, 0, 0.85);
}
</style>
