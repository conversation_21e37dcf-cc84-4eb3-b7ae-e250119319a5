<template>
  <div class="medicine-service">
    <CommonTable
      :tableData="tableData"
      :colums="colums"
      :border="false"
      :total="total"
      :page-size="pageSize"
      :current-page="currentPage"
      @page-size-change="handleSizeChange"
      @current-page-change="handleCurrentChange"
    >
      <template #operation="scope">
        <el-button type="text" @click="checkOut('查看', scope.scope)"
          >详情</el-button
        >
      </template>
    </CommonTable>
  </div>
</template>
<script>
import CommonTable from "@/components/comTable/commonTable.vue";
import { getMedicineService } from "@/api/ogw/index.js";
export default {
  name: "medicineService",
  components: {
    CommonTable,
  },
  props: {
    date: {
      type: String,
    },
  },
  watch: {
    date: {
      handler() {
        this.getTableData();
      },
    },
    allTableData: {
      handler() {
        this.total = this.allTableData.length;
      },
      deep: true,
    },
    paginatedTableData: {
      handler() {
        this.tableData = this.paginatedTableData;
      },
      deep: true,
    },
  },
  mounted() {
    this.getTableData();
  },
  computed: {
    paginatedTableData() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.allTableData.slice(start, end);
    },
  },
  data() {
    return {
      pageSize: 5,
      currentPage: 1,
      total: 0,
      allTableData: [],
      tableData: [],
      colums: [
        { prop: "productionName", label: "平台名称" },
        { prop: "serveName", label: "服务项目名称" },
        { prop: "serveTime", label: "服务时间" },
        { prop: "budget", label: "预算(万元)" },
        { prop: "execute", label: "预算执行(万元)", width: 120 },
        { prop: "operation", label: "操作", fixed: "right", minWidth: 100 },
      ],
    };
  },
  methods: {
    checkOut(type, scope) {
      console.log(type, scope.row);
      switch (scope.row.serveType) {
        case 1:
          this.$router.push({ name: "filterChemical" });
          break;
        case 2:
          this.$router.push({ name: "evaluateChemical" });
          break;
        default:
          this.$router.push({ name: "testChemical" });
          break;
      }
    },
    handleSizeChange(newSize) {
      this.pageSize = newSize;
      this.currentPage = 1; // 重置到第一页
    },
    handleCurrentChange(newPage) {
      this.currentPage = newPage;
    },
    async getTableData() {
      const res = await getMedicineService(this.date);
      if (res.code === 200) {
        this.allTableData = res.data || [];
        this.allTableData.forEach((item) => {
          item.serveTime = item.serveTime.split(" ")[0];
          item.budget = (item.budget / 10000).toFixed(2);
          item.execute = (item.execute / 10000).toFixed(2);
        });
        this.total = this.allTableData.length;
        this.currentPage = 1;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.medicine-service {
  margin: 16px;
}
</style>
