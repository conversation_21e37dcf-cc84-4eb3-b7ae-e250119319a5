<template>
  <div class="item-card">
    <div class="title-box">
      {{ info.title }}
    </div>
    <div class="content-box">
      <div class="amount">
        <span class="num">100</span>
        <span class="unit">万吨</span>
      </div>
      <div class="img-box"></div>
      <div class="rate-box">
        <div class="rate-item">
          <span class="label">同期预算执行</span>
          <span class="value">10%</span>
        </div>
        <div class="rate-item">
          <span class="label">同比</span>
          <span class="value">10%</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: "ItemCard",
  props: {
    info: {
      type: Object,
      default: () => {},
    },
  },
};
</script>
<style lang="scss" scoped>
.item-card {
  width: 100%;
  box-sizing: border-box;
  border-radius: 8px;

  .title-box {
    height: 40px;
    font-family: Source Han Sans;
    font-size: 14px;
    font-weight: normal;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .content-box {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    .num {
      font-family: D-DIN;
      font-size: 48px;
      font-weight: bold;
      margin-right: 8px;
    }
    .unit {
      font-family: Source Han Sans;
      font-size: 16px;
      font-weight: bold;
    }
    .img-box {
      width: 112px;
      height: 120px;
      background-size: 100% 100%;
      margin-bottom: 12px;
    }

    .rate-box {
      width: 60%;
      .rate-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        font-family: Source Han Sans;
        font-size: 14px;
      }
    }
  }
}

[data-theme="dark"] .item-card {
  background: #1a2e5e;
  border: 1px solid rgba(23, 131, 255, 0.5);
  .title-box {
    background: rgba(27, 126, 242, 0.16);
    color: #fff;
  }
  .amount {
    font-variation-settings: "opsz" auto;
    font-feature-settings: "kern" on;
    background: linear-gradient(180deg, #ffffff 0%, #75d8ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
  }
  .img-box {
    background-image: url("@/assets/tableicon/oilcard-darkicon.png");
  }
  .rate-box {
    color: #fff;
  }
}
</style>
