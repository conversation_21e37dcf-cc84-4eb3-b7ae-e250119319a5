<template>
  <div class="comparison-chart">
    <div class="chart-box" ref="chartBox"></div>
    <div class="custom-legend">
      <div 
        v-for="(item, index) in legendData" 
        :key="item.name"
        class="legend-item"
      >
        <span 
          class="legend-color" 
          :style="{ backgroundColor: item.color }"
        ></span>
        <span class="legend-text">{{ item.name }}</span>
      </div>
    </div>
  </div>
</template>
<script>
import * as echarts from "echarts";
export default {
  name: "comparisonChart",
  props: {},
  data() {
    return {
      legendData: [
        { name: "总产量LS17-2", color: "#248EFF" },
        { name: "总产量YC13-1", color: "#58CFFF" },
        { name: "总产量WC16-2", color: "#7262FD" }
      ]
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  methods: {
    initChart() {
      let mychart = echarts.init(this.$refs.chartBox);
      const option = {
        title: {
          text: "",
          left: "center",
          textStyle: {
            color: "#1a1b4e",
            fontStyle: "normal",
            fontSize: 30,
          },
        },
        tooltip: {
          trigger: "item",
          formatter: "{b}",
        },
        series: [
          {
            type: "treemap",
            left: "center",
            width: "80%",
            height: "75%",
            breadcrumb: {
              show: false,
            },
            itemStyle: {
              normal: {
                label: {
                  show: true,
                  formatter: "{b}",
                  position: "insideBottomLeft",
                },
                borderWidth: 2,
                borderColor: "transparent",
                gapWidth: 2,
              },
              emphasis: {
                label: {
                  show: true,
                },
              },
            },
            label: {
              normal: {
                fontSize: 17,
                position: "insideBottomLeft",
              },
            },
            data: [
              {
                name: "总产量LS17-2",
                value: 64.66,
              },
              {
                name: "总产量YC13-1",
                value: 35.34,
              },
              {
                name: "总产量WC16-2",
                value: 35.34,
              },
            ],
          },
        ],
        color: ["#248EFF", "#58CFFF","#7262FD"],
      };
      mychart.setOption(option);
    },
  },
};
</script>
<style lang="scss" scoped>
.comparison-chart {
  width: 100%;
  .chart-box {
    width: 100%;
    height: 200px;
  }
  .custom-legend {
    display: flex;
    justify-content: center;
    margin-top: 10px;
    .legend-item {
      display: flex;
      align-items: center;
      margin: 0 15px;
      .legend-color {
        width: 12px;
        height: 12px;
        border-radius: 2px;
        margin-right: 6px;
      }
      .legend-text {
        color: #fff;
        font-size: 14px;
      }
    }
  }
}
</style>
