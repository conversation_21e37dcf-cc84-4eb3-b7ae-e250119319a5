<template>
  <div class="trend-chart">
    <div class="chart-box" ref="chartBox"></div>
  </div>
</template>
<script>
import * as echarts from "echarts";
export default {
  name: "trendChart",
  data() {
    return {
      xData: ["1月", "2月", "3月", "4月", "5月"],
      y1Data: [148, 152, 171, 154, 201],
      y2Data: [136, 156, 193, 106, 201],
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  methods: {
    initChart() {
      let mychart = echarts.init(this.$refs.chartBox);
      this.option = {
        color: ["#248EFF", "#7262FD"], //圆柱体颜色
        tooltip: {
          trigger: "item",
          padding: 1,
          formatter: function (param) {},
        },
        grid: {
          top: "14%",
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: this.xData,
            axisTick: {
              alignWithLabel: true,
            },
            nameTextStyle: {
              color: "#ACC2E2", //文本颜色
            },
            axisLine: {
              lineStyle: {
                color: "rgba(172, 194, 226, 0.2)", //轴线颜色
              },
            },
            axisLabel: {
              textStyle: {
                color: "#ACC2E2", //轴线文本颜色
              },
            },
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "亿方",
            position: "left",
            nameTextStyle: {
              color: "#ACC2E2",
              align: "right",
              padding: [0, 10, 0, 0],
            },
            axisLabel: {
              textStyle: {
                color: "#ACC2E2",
              },
              formatter: "{value}",
            },
            splitLine: {
              show: false,
            },
          },
        ],
        series: [
          {
            name: "产量",
            type: "pictorialBar",
            symbolSize: [20, 10],
            symbolOffset: [-12, -5],
            symbolPosition: "end",
            z: 12,
            label: {
              normal: {
                show: false,
              },
            },
            data: this.y1Data,
          },
          {
            name: "产量",
            type: "pictorialBar",
            symbolSize: [20, 10],
            symbolOffset: [-12, 5],
            z: 12,
            data: this.y1Data,
          },
          {
            name: "产量",
            type: "bar",
            itemStyle: {
              normal: {
                opacity: 0.7,
              },
            },
            barWidth: "20",
            data: this.y1Data,
          },
          {
            name: "同期",
            type: "pictorialBar",
            symbolSize: [20, 10],
            symbolOffset: [12, -5],
            symbolPosition: "end",
            z: 12,
            label: {
              normal: {
                show: false,
              },
            },
            data: this.y2Data,
          },
          {
            name: "同期",
            type: "pictorialBar",
            symbolSize: [20, 10],
            symbolOffset: [12, 5],
            z: 12,
            data: this.y2Data,
          },
          {
            name: "同期",
            type: "bar",
            itemStyle: {
              normal: {
                opacity: 0.7,
              },
            },
            barWidth: "20",
            data: this.y2Data,
          },
          // {
          //   name: "崖城13-1",
          //   type: "line",
          //   data: [45, 52, 88, 42, 95],
          //   itemStyle: {
          //     color: "#FF6660", // 设置线条颜色为红色
          //   },
          // },
          // {
          //   name: "崖城13-10",
          //   type: "line",
          //   data: [30, 33, 40, 42, 55],
          //   itemStyle: {
          //     color: "#F7AE44", // 设置线条颜色为红色
          //   },
          // },
        ],
      };
      mychart.setOption(this.option);
    },
  },
};
</script>
<style lang="scss" scoped>
.trend-chart {
  width: 100%;
  .chart-box {
    width: 100%;
    height: 280px;
  }
}
</style>
