<template>
  <div class="test-container">
    <h2>折线图Y轴修复测试页面</h2>
    
    <div class="test-section">
      <h3>单数据点测试（模拟你的问题场景）</h3>
      <div class="chart-wrapper">
        <LineChart
          :legendData="singlePointLegend"
          :xData="singlePointXData"
          :yData="singlePointYData"
        />
      </div>
      <div class="data-info">
        <p><strong>数据说明：</strong>只有一个数据点 21068.9，年份 2025</p>
        <p><strong>修复效果：</strong></p>
        <ul>
          <li>Y轴显示纯数字格式，无单位后缀</li>
          <li>合理的刻度范围（约16854.3 - 25282.7）</li>
          <li>显示平均值线条（即使只有一个数据点）</li>
          <li>数值完整显示，不被截断</li>
        </ul>
      </div>
    </div>

    <div class="test-section">
      <h3>多数据点测试</h3>
      <div class="chart-wrapper">
        <LineChart
          :legendData="multiPointLegend"
          :xData="multiPointXData"
          :yData="multiPointYData"
        />
      </div>
      <div class="data-info">
        <p><strong>数据说明：</strong>多个数据点，测试正常情况下的显示效果</p>
        <p><strong>验证点：</strong>平均值线条显示、Y轴刻度合理分布</p>
      </div>
    </div>

    <div class="test-section">
      <h3>小数值测试</h3>
      <div class="chart-wrapper">
        <LineChart
          :legendData="smallValueLegend"
          :xData="smallValueXData"
          :yData="smallValueYData"
        />
      </div>
      <div class="data-info">
        <p><strong>数据说明：</strong>小数值数据，测试数值精度显示</p>
        <p><strong>验证点：</strong>Y轴标签保留合适的小数位数</p>
      </div>
    </div>

    <div class="test-section">
      <h3>原始数据</h3>
      <pre>{{ JSON.stringify(originalData, null, 2) }}</pre>
    </div>

    <div class="test-section">
      <h3>实际组件测试</h3>
      <div class="unit-dosage-test">
        <UnitDosage />
      </div>
    </div>
  </div>
</template>

<script>
import LineChart from "@/components/charts/line/linesChart.vue";
import UnitDosage from "@/views/businessAnalysis/ogw/unitDosage/index.vue";

export default {
  name: "LineChartTest",
  components: {
    LineChart,
    UnitDosage
  },
  data() {
    return {
      // 模拟你的原始数据
      originalData: {
        "code": 200,
        "data": [
          {
            "data": [
              {
                "year": 2025,
                "m3l": 21068.9074860868
              }
            ],
            "chemical": "三甘醇",
            "deviceName": "陵水17-2"
          }
        ],
        "msg": "查询成功"
      },
      
      // 单数据点测试数据
      singlePointLegend: ["陵水17-2"],
      singlePointXData: [2025],
      singlePointYData: [[21068.9074860868]],
      
      // 多数据点测试数据
      multiPointLegend: ["陵水17-2", "陵水18-1"],
      multiPointXData: [2023, 2024, 2025],
      multiPointYData: [
        [18000, 19500, 21068.9], // 陵水17-2
        [15000, 16800, 18200]    // 陵水18-1
      ],

      // 小数值测试数据
      smallValueLegend: ["设备A"],
      smallValueXData: [2023, 2024, 2025],
      smallValueYData: [
        [12.345, 15.678, 18.901] // 小数值测试
      ]
    };
  },
  mounted() {
    console.log("折线图测试页面加载完成");
    console.log("单数据点测试数据:", {
      legend: this.singlePointLegend,
      xData: this.singlePointXData,
      yData: this.singlePointYData
    });
  }
};
</script>

<style lang="scss" scoped>
.test-container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;

  h2 {
    color: #333;
    margin-bottom: 30px;
    text-align: center;
  }

  .test-section {
    margin-bottom: 40px;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background: #f9f9f9;

    h3 {
      color: #555;
      margin-bottom: 20px;
      border-bottom: 2px solid #007bff;
      padding-bottom: 10px;
    }

    .chart-wrapper {
      height: 300px;
      width: 100%;
      background: #0e2147;
      border-radius: 4px;
      padding: 20px;
      margin-bottom: 15px;
    }

    .unit-dosage-test {
      height: 500px;
      background: white;
      border-radius: 4px;
      padding: 20px;
    }

    .data-info {
      background: #e8f4fd;
      padding: 15px;
      border-radius: 4px;
      border-left: 4px solid #007bff;
      
      p {
        margin: 5px 0;
        font-size: 14px;
        
        strong {
          color: #007bff;
        }
      }
    }

    pre {
      background: #f4f4f4;
      padding: 15px;
      border-radius: 4px;
      overflow-x: auto;
      font-size: 12px;
      line-height: 1.4;
      max-height: 300px;
      overflow-y: auto;
    }
  }
}

[data-theme="dark"] .test-container {
  .test-section {
    background: #2d2d2d;
    border-color: #555;

    h3 {
      color: #fff;
      border-bottom-color: #007bff;
    }

    .unit-dosage-test {
      background: #1a1a1a;
    }

    .data-info {
      background: #1a3a5c;
      border-left-color: #007bff;
      
      p {
        color: #fff;
        
        strong {
          color: #00bfff;
        }
      }
    }

    pre {
      background: #1a1a1a;
      color: #fff;
    }
  }
}
</style>
